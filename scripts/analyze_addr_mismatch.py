#!/usr/bin/env python3
"""
分析pilot.log中addr_mismatch数据的脚本
绘制addr_mismatch数据随时间变化的折线图
"""

import re
import argparse
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import sys

def parse_log_line(line):
    """
    解析日志行，提取时间戳和addr_mismatch值
    
    Args:
        line (str): 日志行
        
    Returns:
        tuple: (datetime对象, addr_mismatch值) 或 (None, None)
    """
    # 匹配包含addr_mismatch的行
    if 'addr_mismatch:' not in line:
        return None, None
    
    # 提取时间戳 [2027-01-25 00:00:01.625]
    timestamp_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\]'
    timestamp_match = re.search(timestamp_pattern, line)
    
    # 提取addr_mismatch值
    addr_mismatch_pattern = r'addr_mismatch:(\d+)'
    addr_mismatch_match = re.search(addr_mismatch_pattern, line)
    
    if timestamp_match and addr_mismatch_match:
        try:
            # 解析时间戳
            timestamp_str = timestamp_match.group(1)
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
            
            # 解析addr_mismatch值
            addr_mismatch_value = int(addr_mismatch_match.group(1))
            
            return timestamp, addr_mismatch_value
        except (ValueError, IndexError):
            return None, None
    
    return None, None

def analyze_log_file(file_path):
    """
    分析日志文件，提取所有addr_mismatch数据
    
    Args:
        file_path (str): 日志文件路径
        
    Returns:
        tuple: (时间戳列表, addr_mismatch值列表)
    """
    timestamps = []
    addr_mismatch_values = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            for line_num, line in enumerate(file, 1):
                timestamp, addr_mismatch_value = parse_log_line(line)
                if timestamp is not None and addr_mismatch_value is not None:
                    timestamps.append(timestamp)
                    addr_mismatch_values.append(addr_mismatch_value)
                    
        print(f"Successfully parsed {len(timestamps)} addr_mismatch records")

        if timestamps:
            print(f"Time range: {timestamps[0]} to {timestamps[-1]}")
            print(f"Addr_mismatch value range: {min(addr_mismatch_values)} to {max(addr_mismatch_values)}")
        
        return timestamps, addr_mismatch_values
        
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error: Exception occurred while reading file: {e}")
        sys.exit(1)

def plot_addr_mismatch_data(timestamps, addr_mismatch_values, output_file=None):
    """
    绘制addr_mismatch数据随时间变化的折线图
    
    Args:
        timestamps (list): 时间戳列表
        addr_mismatch_values (list): addr_mismatch值列表
        output_file (str): 输出图片文件路径（可选）
    """
    if not timestamps:
        print("Warning: No addr_mismatch data found")
        return
    
    # 设置字体，优先使用英文避免中文字体问题
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 绘制折线图
    ax.plot(timestamps, addr_mismatch_values, 'b-', linewidth=1.5, marker='o', markersize=3)
    
    # 设置标题和标签（使用英文避免字体问题）
    ax.set_title('Addr Mismatch Data Over Time', fontsize=16, fontweight='bold')
    ax.set_xlabel('Time', fontsize=12)
    ax.set_ylabel('Addr Mismatch Value', fontsize=12)
    
    # 格式化x轴时间显示
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
    ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=1))
    
    # 旋转x轴标签以避免重叠
    plt.xticks(rotation=45)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    mean_value = sum(addr_mismatch_values) / len(addr_mismatch_values)
    max_value = max(addr_mismatch_values)
    min_value = min(addr_mismatch_values)
    
    # 在图上添加统计信息文本（使用英文）
    stats_text = f'Statistics:\nAverage: {mean_value:.1f}\nMax: {max_value}\nMin: {min_value}\nData Points: {len(addr_mismatch_values)}'
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存或显示图形
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {output_file}")
    else:
        plt.show()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析pilot.log中的addr_mismatch数据并绘制折线图')
    parser.add_argument('log_file', help='待分析的日志文件路径')
    parser.add_argument('-o', '--output', help='输出图片文件路径（可选，默认显示图表）')
    
    args = parser.parse_args()
    
    print(f"Analyzing file: {args.log_file}")
    
    # 分析日志文件
    timestamps, addr_mismatch_values = analyze_log_file(args.log_file)
    
    # 绘制图表
    plot_addr_mismatch_data(timestamps, addr_mismatch_values, args.output)

if __name__ == '__main__':
    main()
