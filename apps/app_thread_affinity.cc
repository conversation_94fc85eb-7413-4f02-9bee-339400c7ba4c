#define _GNU_SOURCE
#include <stdio.h>
#include <sched.h>
#include <unistd.h>
#include <pthread.h>
#include <cstdint>

static void print_cpu_set_mask(cpu_set_t *set)
{
    uint32_t mask = 0;
    for (int i = 0; i < 4; i++)
    {
        if (CPU_ISSET(i, set))
        {
            printf("CPU %d is set\n", i);
            mask |= 1 << i;
        }
    }
    printf("mask:0x%x\n", mask);
}

static void set_cpu_affinity_by_mask(cpu_set_t *set, uint32_t mask)
{
    CPU_ZERO(set);
    for (int i = 0; i < 4; i++)
    {
        if (mask & (1 << i))
        {
            CPU_SET(i, set);
        }
    }
}

void *thread_func(void *arg)
{
    cpu_set_t mask;
    CPU_ZERO(&mask);
    CPU_SET(1, &mask); // 绑到 CPU1

    pthread_t thread = pthread_self();
    if (pthread_setaffinity_np(thread, sizeof(mask), &mask) != 0)
    {
        perror("pthread_setaffinity_np");
    }

    while (1)
    {
    }
    return NULL;
}

int main()
{
    cpu_set_t mask;
    uint32_t readable_mask = 0xb;
    set_cpu_affinity_by_mask(&mask, readable_mask);

    pid_t pid = getpid();
    if (sched_setaffinity(pid, sizeof(mask), &mask) == -1)
    {
        perror("sched_setaffinity");
        return 1;
    }

    printf("Process %d is bound to CPU 0x%x\n", pid, readable_mask);

    // 获取当前线程的亲和性
    cpu_set_t get_mask;
    sched_getaffinity(pid, sizeof(get_mask), &get_mask);
    print_cpu_set_mask(&get_mask);

    while (1)
    {
        // 模拟工作
    }

    return 0;
}
