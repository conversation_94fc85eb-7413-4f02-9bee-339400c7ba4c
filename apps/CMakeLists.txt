add_compile_definitions(AR9481)

set(CMAKE_C_FLAGS "-Wno-deprecated -Wno-unused-parameter -Wno-unused-function -Wno-unused-variable \
							-Wno-write-strings -Wno-missing-field-initializers -Wno-sequence-point -Wno-format \
							-Wno-format-extra-args -Wno-type-limits -Wno-attributes -Wno-unused-but-set-variable \
                            -Wno-reserved-user-defined-literal")
set(CMAKE_CXX_FLAGS "-Wno-unused-parameter -Wno-sign-compare -Wno-implicit-fallthrough \
							-Wno-unused-function -Wno-attributes \
							-Wno-missing-field-initializers -Wno-unused-but-set-variable -Wno-shift-negative-value \
                            -Wno-reserved-user-defined-literal -fpermissive")

add_library( app_base INTERFACE )

add_subdirectory(stubs)
add_subdirectory(94_board)

target_sources( app_base INTERFACE
    )

target_include_directories( app_base INTERFACE
    )

target_link_libraries( app_base
    INTERFACE
    stub
    nr_flinger_static
    warpcore::warpcore
    )

# add apps
APP_TARGET_WITH_STRIP(app_thread_affinity app_thread_affinity.cc app_base)
APP_TARGET_WITH_STRIP(app_gen_debug_warp_bin app_gen_debug_warp_bin.cc app_base)
APP_TARGET_WITH_STRIP(app_do_warp app_do_warp.cc app_base)
APP_TARGET_WITH_STRIP(app_geometry_test app_geometry_test.cc app_base)
APP_TARGET_WITH_STRIP(app_gen_weight_dat app_gen_weight_dat.cc app_base)
APP_TARGET_WITH_STRIP(app_decode_frame_embedded_info app_decode_frame_embedded_info.cc app_base)
#APP_TARGET_WITH_STRIP(app_dump_register app_dump_register.cc app_base)
#APP_TARGET_WITH_STRIP(app_pluginload app_pluginload.cc app_base)
APP_TARGET_WITH_STRIP(app_lifecycle app_lifecycle.cc app_base)
#APP_TARGET_WITH_STRIP(app_space_screen app_space_screen.cc app_base)
#APP_TARGET_WITH_STRIP(app_flinger_provider_test_rgba app_flinger_provider_test_rgba.cc app_base)
#APP_TARGET_WITH_STRIP(app_interface_stub_test app_interface_stub_test.cc app_base)
APP_TARGET_WITH_STRIP(app_parse_glasses_config app_parse_glasses_config.cc app_base)
APP_TARGET_WITH_STRIP(app_interpolate_distortion_mesh app_interpolate_distortion_mesh.cc app_base)
APP_TARGET_WITH_STRIP(app_gdc_file_in_file_out app_gdc_file_in_file_out.cc app_base)

APP_TARGET_WITH_STRIP(debug_warp debug_warp.cc app_base)

APP_TARGET_WITH_STRIP(dummy_threads app_dummy_threads.cc app_base)