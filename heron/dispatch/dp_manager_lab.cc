#include <heron/dispatch/dp_manager.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/control/control_display.h>
#include <heron/control/control_dp.h>
#include <heron/util/log.h>

using namespace heron;
using namespace control;
using namespace dispatch;
using namespace model;

bool DpManager::AllocShiftedFrame(uint32_t width, uint32_t height)
{
    for (uint32_t i = 0; i < ModelManager::SHIFTED_FRAME_POOL_SIZE; ++i)
    {
        shifted_frame_pool_.emplace_back();
        string name = "shifted_" + std::to_string(i);
        if (!AllocShiftedFrameInternal(width, height, shifted_frame_pool_[i], name.c_str()))
            return false;
    }
    return true;
}

bool DpManager::AllocShiftedFrameInternal(uint32_t width, uint32_t height, DpFrameData &dp_frame_data, const char *name)
{
    uint32_t frame_y_stride = ALIGN128(width);
    uint32_t frame_uv_stride = ALIGN128(width / 2);
    uint32_t frame_y_size = frame_y_stride * height;
    uint32_t frame_uv_size = frame_uv_stride * height / 2;
    uint32_t frame_width = width;
    uint32_t frame_height = height;
    uint32_t size = frame_y_size + frame_uv_size * 2;
    void *virtual_address;
    uint64_t physical_address;
    if (DebugManager::GetInstance()->mmz_cached_for_shifted_frame)
    {
        if (!DispatcherWrapper::GetInstance()->ARMmzAllocCached(
                &physical_address, &virtual_address, name, nullptr, size))
        {
            HERON_LOG_ERROR("ARMmzAllocCached for {} failed.", name);
            return false;
        }
    }
    else
    {
        if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(
                &physical_address, &virtual_address, name, nullptr, size))
        {
            HERON_LOG_ERROR("ARMmzAlloc for {} failed.", name);
            return false;
        }
    }
    // get frame
    dp_frame_data.ar_frame_handle = 0;
    dp_frame_data.data[0] = (char *)virtual_address;
    dp_frame_data.data[1] = dp_frame_data.data[0] + frame_y_size;
    dp_frame_data.data[2] = dp_frame_data.data[1] + frame_uv_size;
    dp_frame_data.data_ext[0] = (char *)physical_address;
    dp_frame_data.data_ext[1] = dp_frame_data.data_ext[0] + frame_y_size;
    dp_frame_data.data_ext[2] = dp_frame_data.data_ext[1] + frame_uv_size;
    dp_frame_data.frame_id = 0;
    dp_frame_data.width = frame_width;
    dp_frame_data.height = frame_height;
    dp_frame_data.strides[0] = frame_y_stride;
    dp_frame_data.strides[1] = frame_uv_stride;
    dp_frame_data.strides[2] = frame_uv_stride;
    dp_frame_data.pts = 0;
    dp_frame_data.pixel_format = FRAMEBUFFER_FORMAT_YUV420_PLANAR;
    HERON_LOG_DEBUG("shifted frame {} allocated: {}x{}", name, width, height);
    return true;
}

void DpManager::FreeShiftedFrame(DpFrameData &dp_frame_data)
{
    if (dp_frame_data.data[0])
        DispatcherWrapper::GetInstance()->ARMmzDealloc((uint64_t)dp_frame_data.data_ext[0], (void *)dp_frame_data.data[0]);
}

void DpManager::ReceiveShiftedFrameSrc()
{
    HERON_LOG_INFO("ReceiveShiftedFrameSrc started");
#ifdef HERON_SYSTEM_XRLINUX
    pthread_setname_np(pthread_self(), "receive_shifted_frame_src");
#endif
    while (running_)
    {
        // get frame
        FramePtr frame = fresh_frame_queue_.Pop();
        FrameInfo buffer{};
        buffer.nr_dp_frame = (void *)frame;
        if (!DispatcherWrapper::GetInstance()->DpGetFrame(&buffer, 3000))
        {
            buffer.nr_dp_frame = nullptr;
            // XXX : no need to release nr_dp_frame on error
            fresh_frame_queue_.Push(frame);
            // XXX : should not call DoneWriteBuffer() here, because, we didn't actually got the frame.
            // GetWritableBuffer in the next run will return the same index
            continue;
        }
        uint64_t dp_rx_done_ns = GetTimeNano();

        DebugManager *p_dm = DebugManager::GetInstance();
        uint32_t target_width = p_dm->target_src_size_pixel.x();
        uint32_t target_height = p_dm->target_src_size_pixel.y();
        if (buffer.dp_frame_data.height != target_height || buffer.dp_frame_data.width != target_width)
            DispatcherWrapper::GetInstance()->DpResizeFrame(target_width, target_height);

        if (p_dm->debug_log.dp_frame_data_detail)
        {
            HERON_LOG_DEBUG("{:.3f} ReceiveShiftedFrame {} data_ext:{}", buffer.metadata.timing.dp_rx_done_ns / 1000000000.0,
                            buffer.nr_dp_frame, (void *)buffer.dp_frame_data.data_ext[0]);
        }
        // DpGetFrame success
        fps_counter_.Update();
        bool hold_src_data = false;
        buffer.dp_frame_data.ar_frame_handle = (uint64_t)buffer.nr_dp_frame;
        FlingerInterface::GetInstance()->GenDepthShiftedImage(p_dm->shift_frame_src_component, &buffer.dp_frame_data,
                                                              COMPONENT_INVALID, nullptr, &hold_src_data);
        if (!hold_src_data)
        {
            release_frame_task_queue_.AddTask([frame_info = std::move(buffer)]() mutable
                                              { DispatcherWrapper::GetInstance()->DpReleaseFrame(&frame_info); }); // XXX: don't use buffer afterwards
            fresh_frame_queue_.Push(frame);
        }
        else
        {
            hold_src_fps_counter_.Update();
            if (p_dm->debug_log.dp_frame_data_detail)
            {
                HERON_LOG_DEBUG("hold_src_data on ReceiveShiftedFrameSrc ext:{} FramePtr:{}", (void *)buffer.dp_frame_data.data_ext[0], (void *)frame);
            }
        }
        recv_frame_cost_.Update(GetTimeNano() - dp_rx_done_ns);
    }
    HERON_LOG_INFO("Dp ReceiveShiftedFrameSrc thread quit.");
}

static uint32_t s_acquire_count = 0;
bool DpManager::AcquireWritableDpFrameData(const DpFrameData **dst_frame_data)
{
    shifted_frame_pool_[current_shifted_frame_idx_].frame_id = s_acquire_count++;
    *dst_frame_data = &shifted_frame_pool_[current_shifted_frame_idx_];
    return true;
}

bool DpManager::SubmitStereoDpFrameData(const DpFrameData *frame_data_dst, const DpFrameData *frame_data_src)
{
    shifted_fps_counter_.Update();
    FrameInfosPtr frame_infos = ModelManager::GetInstance()->GetFrameInfos();
    FrameInfo *buffer = frame_infos->GetWritableBuffer();
    if (!buffer)
    {
        HERON_LOG_ERROR("dp manager get writable buffer failed on SubmitStereoDpFrameData.");
        return false;
    }
    // free frame first
    FramePtr old_frame = static_cast<FramePtr>(buffer->nr_dp_frame);
    DebugManager *p_dm = DebugManager::GetInstance();
    if (p_dm->debug_log.dp_frame_data_detail)
    {
        HERON_LOG_DEBUG("hold_src_data on SubmitStereoDpFrameData ext:{} frame_ptr:{} old_frame:{}",
                        (void *)frame_data_src->data_ext[0],
                        (void *)frame_data_src->ar_frame_handle, (void *)old_frame);
    }
    if (!old_frame)
    {
        HERON_LOG_DEBUG("old_frame null on SubmitStereoDpFrameData.");
    }
    else
    {
        FrameInfo local_frame = *buffer; // deep copy to avoid release frame task modify the buffer
        release_frame_task_queue_.AddTask([frame = std::move(local_frame)]() mutable
                                          { DispatcherWrapper::GetInstance()->DpReleaseFrame(&frame); });
        fresh_frame_queue_.Push(old_frame);
    }
    buffer->dp_frame_data = *frame_data_src;
    buffer->nr_dp_frame = (void *)(frame_data_src->ar_frame_handle);
    buffer->gotten = true;
    if (DpCtrl::GetInstance()->ParseFrame(*buffer))
    {
        consecutive_valid_frame_count_++;
        if (buffer->space_screen_status.validation.bad_view)
            consecutive_valid_frame_count_ = 0;
        buffer->space_screen_status.validation.first_frames_to_ignore = consecutive_valid_frame_count_ < ModelManager::FIRST_FRAMES_TO_IGNORE;
        DpCtrl::GetInstance()->UpdateSceneMode(SCENE_MODE_SPACE_SCREEN);
        ModelManager::GetInstance()->UpdateQuadStatus();
        shifted_frame_pool_[current_shifted_frame_idx_] = *frame_data_dst;
        buffer->depth_shifted_frame_data = &shifted_frame_pool_[current_shifted_frame_idx_];
        if (p_dm->mmz_cached_for_shifted_frame)
        {
            uint64_t start = GetTimeNano();
            DispatcherWrapper::GetInstance()->ARMmzFlushCache((uint64_t)frame_data_dst->data_ext[0], (void *)frame_data_dst->data[0],
                                                              frame_data_dst->strides[0] * frame_data_dst->height + frame_data_dst->strides[1] * frame_data_dst->height);
            flush_shifted_image_cache_.Update(GetTimeNano() - start);
        }
        ++current_shifted_frame_idx_;
        if (current_shifted_frame_idx_ >= ModelManager::SHIFTED_FRAME_POOL_SIZE)
            current_shifted_frame_idx_ = 0;
    }
    frame_infos->DoneWriteBuffer();
    return true;
}

void DpManager::MaybeConfigureGDC(FrameInfo *frame_info)
{
    GlassesMode mode = ModelManager::GetInstance()->GetGlassesMode();
    if (mode != GLASSES_MODE_BYPASS && mode != GLASSES_MODE_DISPLAY)
        return;
    if (frame_info->space_screen_status.validation.NotToPresent())
        return;
    if (!need_direct_gdc_configure_)
        return;
    HERON_LOG_DEBUG("MaybeConfigureGDC {} size_pixel:{}x{}", (void *)frame_info->dp_frame_data.data_ext[0], frame_info->dp_frame_data.width, frame_info->dp_frame_data.height);
    DisplayCtrl::GetInstance()->GdcDirectConfigure(frame_info->dp_frame_data, frame_info->space_screen_status);
    need_direct_gdc_configure_ = false;
}
