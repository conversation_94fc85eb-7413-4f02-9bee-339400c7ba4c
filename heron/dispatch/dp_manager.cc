#include <heron/dispatch/dp_manager.h>
#include <heron/dispatch/gdc_manager.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/dispatch/dispatcher.h>
#include <heron/control/control_display.h>
#include <heron/control/control_dp.h>
#include <heron/model/model_manager.h>
#include <heron/message/m_type_converter.h>
#include <heron/util/warp.h>
#include <heron/util/debug.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>
#include <heron/message/message.h>
#include <heron/interface_provider/device_message.h>

#include <framework/util/trace_manager.h>

using namespace heron;
using namespace control;
using namespace dispatch;
using namespace model;
using namespace message;
using namespace interface_provider;

DpManager::DpManager() : fresh_frame_queue_(FRAME_BUFFER_COUNT), release_frame_task_queue_("release_dp_frame", 3)
{
    // HERON_LOG_TRACE("DpManager constructor");
    frames_.reserve(FRAME_BUFFER_COUNT);
    for (int32_t i = 0; i < FRAME_BUFFER_COUNT; ++i)
        frames_.emplace_back(std::make_unique<NRDpFrameData>());
    fresh_frames_.reserve(FRAME_BUFFER_COUNT);
    Init();
}

DpManager::~DpManager()
{
    for (uint32_t i = 0; i < shifted_frame_pool_.size(); ++i)
        FreeShiftedFrame(shifted_frame_pool_[i]);
}

void DpManager::Init()
{
    for (auto &frame : frames_)
    {
        fresh_frames_.emplace_back(frame.get());
        fresh_frame_queue_.Push(frame.get());
    }
    writable_shifted_frame_ = &shifted_frame_pool_[0];
}

void DpManager::Clear()
{
    HERON_LOG_INFO("DpManager Clearing");
    FrameInfosPtr frame_infos = ModelManager::GetInstance()->GetFrameInfos();
    for (int32_t i = 0; i < frame_infos->GetSize(); ++i)
    {
        FrameInfo *frame_info = nullptr;
        if (frame_infos->IsIndexValid(i))
            frame_info = frame_infos->GetBuffer(i);
        if (!frame_info)
        {
            HERON_LOG_ERROR("GetBufferByIdx: {} error. should not happen", i);
            continue;
        }
        FramePtr old_frame = (FramePtr)frame_info->nr_dp_frame;
        if (old_frame)
        {
            frame_info->nr_dp_frame = nullptr;
            FreeFrame(old_frame);
        }
        else
        {
            HERON_LOG_DEBUG("old_frame null on Clear");
        }
    }
    fresh_frames_.clear();

    // HERON_LOG_TRACE("DpManager init in clearing");
    Init();
    HERON_LOG_INFO("DpManager clear done");
}

void DpManager::StartReceiveThread()
{
    if (running_)
        return;
    DpFrameData dummy_frame;
    GDCManager::GetInstance()->GetDummyFrame(dummy_frame);
    DisplayCtrl::GetInstance()->GdcDirectConfigure(dummy_frame, *ModelManager::GetInstance()->GetSpaceScreenStatus(), false, 1);
    running_ = true;
    DebugManager *p_dm = DebugManager::GetInstance();
    if (p_dm->async_shifted_frame && p_dm->gen_depth_shifted_frame)
    {
        release_frame_task_queue_.Start();
        thread_ = std::thread(&DpManager::ReceiveShiftedFrameSrc, this);
    }
    else
    {
        thread_ = std::thread(&DpManager::ReceiveFrame, this);
    }
}

void DpManager::StopReceiveThread()
{
    if (!running_)
        return;
    running_ = false;
    if (thread_.joinable())
        thread_.join();
    DebugManager *p_dm = DebugManager::GetInstance();
    if (p_dm->async_shifted_frame && p_dm->gen_depth_shifted_frame)
    {
        release_frame_task_queue_.Stop();
        fresh_frame_queue_.Stop();
    }
    Clear();
}

// overall logic of receiving frame:
// if (DpGetFrame()) {
//     if(ParseFrame()) {
//         if(ParseEmbeddedData()) {
//             if(PopulateFrameInfoMetadata()) {
//                 SetSceneMode(WithNebula);
//                 PushFrameToBufferQueue();
//             } else {
//                 DiscardFrame();
//             }
//         } else {
//             SetSceneMode(SpaceScreen);
//             PushFrameToBufferQueue();
//         }
//     }
//     else {
//         PushFrameToBufferQueue();(mark frame as invalid)
//     }
// else {
//     continue;
// }
static Average s_get_frame_cost("get_frame_cost");
static Average s_dp_latency("dp_get_frame_latency");
static Average s_dp_interval("dp_interval");
static Average s_gen_vsync("gen_vsync");
static Average s_send_vsync("send_vsync");
static Average s_parse_frame("parse_frame");
static Average s_parse_embedded("parse_embedded");
static Average s_populate_frame("populate_frame");
uint64_t s_last_dp_rx_done = 0;
void DpManager::ReceiveFrame()
{
    HERON_LOG_INFO("ReceiveFrame started");
#ifdef HERON_SYSTEM_XRLINUX
    pthread_setname_np(pthread_self(), "receive_frame");
#endif
    while (running_)
    {
#ifdef HERON_SYSTEM_XRLINUX
        TRACE_EVENT_BEGIN("dp_manager", "pre_GetFrame");
#endif
        FrameInfosPtr frame_infos = ModelManager::GetInstance()->GetFrameInfos();
        FrameInfo *buffer = frame_infos->GetWritableBuffer();
        if (!buffer)
        {
            HERON_LOG_ERROR("dp manager get writable buffer failed! should not happen.");
            continue;
        }
        // free frame first
        FramePtr old_frame = static_cast<FramePtr>(buffer->nr_dp_frame);
        // HERON_LOG_TRACE("returning old frame: {}", buffer->nr_dp_frame);
        if (!old_frame)
        {
            HERON_LOG_DEBUG("old_frame null");
        }
        else
        {
            DispatcherWrapper::GetInstance()->DpReleaseFrame(buffer);
            FreeFrame(old_frame);
        }
        // get frame
        FramePtr frame = AllocFrame();
        buffer->nr_dp_frame = (void *)frame;
#ifdef HERON_SYSTEM_XRLINUX
        TRACE_EVENT_END("dp_manager");
#endif
        uint64_t start_ns = GetTimeNano();
        if (!DispatcherWrapper::GetInstance()->DpGetFrame(buffer, 3000))
        {
            buffer->nr_dp_frame = nullptr;
            // XXX : no need to release nr_dp_frame on error
            FreeFrame(frame);
            // XXX : should not call DoneWriteBuffer() here, because, we didn't actually got the frame.
            // GetWritableBuffer in the next run will return the same index
            continue;
        }
        buffer->metadata.timing.dp_rx_done_ns = GetTimeNano();
        DisplayCtrl::GetInstance()->SetDpRxVsyncPts(buffer->dp_frame_data.pts * 1000);

        DebugManager *p_dm = DebugManager::GetInstance();
        if (p_dm->debug_log.dp_frame_data_detail)
        {
            HERON_LOG_DEBUG("{:.3f} ReceiveFrame valid_count:{} {} data_ext:{} pixel_format:{} strides:{},{},{} frame_pts_us:{} handle:{}", buffer->metadata.timing.dp_rx_done_ns / 1000000000.0,
                            consecutive_valid_frame_count_, buffer->nr_dp_frame, (void *)buffer->dp_frame_data.data_ext[0], buffer->dp_frame_data.pixel_format,
                            buffer->dp_frame_data.strides[0], buffer->dp_frame_data.strides[1], buffer->dp_frame_data.strides[2], buffer->dp_frame_data.pts, buffer->dp_frame_data.ar_frame_handle);
        }
        if (p_dm->sleep_us_after_dp_rx_done > 0)
            usleep(p_dm->sleep_us_after_dp_rx_done);

        int64_t dp_interval = buffer->metadata.timing.dp_rx_done_ns - s_last_dp_rx_done;
        s_last_dp_rx_done = buffer->metadata.timing.dp_rx_done_ns;
        if (p_dm->gen_fake_vsync)
        {
            ByteBuf buf;
            GenerateDpVsyncInfo(buf);
            buffer->metadata.timing.vsync_generated_ns = GetTimeNano();
            DeviceMessageSendInterface::GetInstance()->BroadcastDeviceMessage((const void *)(buf.begin()), buf.size());
            buffer->metadata.timing.vsync_sent_ns = GetTimeNano();
        }
        // DpGetFrame success
        fps_counter_.Update();
        if (!DpCtrl::GetInstance()->ParseFrame(*buffer))
        { // push in to buffer queue anyway when the frame returned by DpGetFrame is considered invalid
            frame_infos->DoneWriteBuffer();
            continue;
        }
        F_TRACE_EVENT("dp_manager", "after_parse_frame");
        consecutive_valid_frame_count_++;
        if (p_dm->gen_depth_shifted_frame)
        {
            bool hold_src_data = false;
            FlingerInterface::GetInstance()->GenDepthShiftedImage(p_dm->shift_frame_src_component, &buffer->dp_frame_data,
                                                                  p_dm->shift_frame_dst_component, writable_shifted_frame_, &hold_src_data);
            if (p_dm->mmz_cached_for_shifted_frame)
            {
                uint64_t start = GetTimeNano();
                DispatcherWrapper::GetInstance()->ARMmzFlushCache((uint64_t)writable_shifted_frame_->data_ext[0], (void *)writable_shifted_frame_->data[0],
                                                                  writable_shifted_frame_->strides[0] * writable_shifted_frame_->height + writable_shifted_frame_->strides[1] * writable_shifted_frame_->height);
                flush_shifted_image_cache_.Update(GetTimeNano() - start);
            }
            buffer->depth_shifted_frame_data = writable_shifted_frame_;
            ++current_shifted_frame_idx_;
            if (current_shifted_frame_idx_ >= ModelManager::SHIFTED_FRAME_POOL_SIZE)
                current_shifted_frame_idx_ = 0;
            writable_shifted_frame_ = &shifted_frame_pool_[current_shifted_frame_idx_];
        }
        DpCtrl::GetInstance()->UpdateSuitableSrcFrameSize(buffer->dp_frame_data.width, buffer->dp_frame_data.height, buffer->space_screen_status, buffer->target_size_factor);
        buffer->metadata.timing.frame_parsed_ns = GetTimeNano();
        FrameEmbeddedInfoSimpleTwin embedded_info_simple;
        FrameEmbeddedInfoTwin embedded_info;
        bool embedded_simple = false;
        bool bw_decode_result = DpCtrl::GetInstance()->ParseEmbeddedData(*buffer, embedded_simple, embedded_info_simple, embedded_info);
        buffer->metadata.timing.embedded_data_parsed_ns = GetTimeNano();
        DpCtrl::GetInstance()->MaybeDumpFrame(*buffer, bw_decode_result); // for debug purpose

        static LateStageReprojectionMode s_last_late_stage_reprojection_mode = WARP_MODE_SPACE;
        if (!bw_decode_result)
        {
            if (buffer->space_screen_status.validation.bad_view)
                consecutive_valid_frame_count_ = 0;
            buffer->space_screen_status.validation.first_frames_to_ignore = consecutive_valid_frame_count_ < ModelManager::FIRST_FRAMES_TO_IGNORE;
            DpCtrl::GetInstance()->UpdateSceneMode(SCENE_MODE_SPACE_SCREEN);
            ModelManager::GetInstance()->UpdateQuadStatus();
            frame_infos->DoneWriteBuffer(); // consider the frame as normal space_screen frame when failed to get valid embedded data
            if (s_last_late_stage_reprojection_mode == WARP_MODE_NONE)
            {
                HERON_LOG_DEBUG("change glasses mode to LSR");
                // host端运行期间异常退出时，需让眼镜回到LSR模式。
                DpCtrl::GetInstance()->SetGlassesMode(GLASSES_MODE_LSR);
                s_last_late_stage_reprojection_mode = WARP_MODE_SPACE;
            }
            MaybeConfigureGDC(buffer);
            PrintDebugTiming(buffer, dp_interval, bw_decode_result, start_ns);
            continue;
        }
        // got valid embedded data
        if (!DpCtrl::GetInstance()->PopulateFrameInfoMetadata(*buffer, embedded_simple, embedded_info_simple, embedded_info))
        { // discard this frame when embedded data is valid but failed to populate metadata
            DispatcherWrapper::GetInstance()->DpReleaseFrame(buffer);
            FreeFrame(frame);
            // XXX : should not call DoneWriteBuffer() here, because, we want to discard the frame.
            // GetWritableBuffer in the next run will return the same index
            continue;
        }
        // overwrite space_screen_status while no need to modify ori value in ModelManager
        buffer->space_screen_status.scene_mode = SCENE_MODE_WITH_NEBULA;
        buffer->space_screen_status.perception_type = PERCEPTION_TYPE_REMOTE;
        buffer->space_screen_status.validation.bad_view = false; // always consider as good view in SCENE_MODE_WITH_NEBULA
        DpCtrl::GetInstance()->UpdateSceneMode(SCENE_MODE_WITH_NEBULA);
        s_last_late_stage_reprojection_mode = buffer->metadata.lsr_mode;
        frame_infos->DoneWriteBuffer();

        MaybeConfigureGDC(buffer);
        PrintDebugTiming(buffer, dp_interval, bw_decode_result, start_ns);
    }
    HERON_LOG_INFO("Dp ReceiveFrame thread quit.");
}

void DpManager::PrintDebugTiming(FrameInfo *buffer, int64_t dp_interval, bool bw_decode_result, uint64_t start_ns)
{
    DebugManager *p_dm = DebugManager::GetInstance();
    uint64_t now = GetTimeNano();
    recv_frame_cost_.Update(now - buffer->metadata.timing.dp_rx_done_ns);
    if (!p_dm->debug_log.timing_detail)
        return;
    s_get_frame_cost.Update(buffer->metadata.timing.dp_rx_done_ns - start_ns);
    s_dp_latency.Update(buffer->metadata.timing.dp_rx_done_ns - buffer->dp_frame_data.pts * 1000);
    s_dp_interval.Update(dp_interval);
    if (p_dm->gen_fake_vsync)
    {
        s_gen_vsync.Update(buffer->metadata.timing.vsync_generated_ns - buffer->metadata.timing.dp_rx_done_ns);
        s_send_vsync.Update(buffer->metadata.timing.vsync_sent_ns - buffer->metadata.timing.vsync_generated_ns);
        s_parse_frame.Update(buffer->metadata.timing.frame_parsed_ns - buffer->metadata.timing.vsync_sent_ns);
    }
    s_parse_frame.Update(buffer->metadata.timing.frame_parsed_ns - buffer->metadata.timing.dp_rx_done_ns);
    if (!bw_decode_result)
        return;
    s_parse_embedded.Update(buffer->metadata.timing.embedded_data_parsed_ns - buffer->metadata.timing.frame_parsed_ns);
    s_populate_frame.Update(now - buffer->metadata.timing.embedded_data_parsed_ns);
}

DpManager::FramePtr DpManager::AllocFrame()
{
    if (fresh_frames_.empty())
    {
        HERON_LOG_ERROR("No free frame");
        return nullptr;
    }
    FramePtr frame = fresh_frames_.back();
    fresh_frames_.pop_back();
    if (DebugManager::GetInstance()->debug_log.dp_frame_data_detail)
    {
        HERON_LOG_DEBUG("AllocFrame:{}", (void *)frame);
    }
    return frame;
}

void DpManager::FreeFrame(FramePtr frame)
{
    if (DebugManager::GetInstance()->debug_log.dp_frame_data_detail)
    {
        HERON_LOG_DEBUG("FreeFrame:{}", (void *)frame);
    }
    fresh_frames_.push_back(frame); // push the frame anyway
}