#include <heron/util/math_tools.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>

#include <map>
#include <optional> // For std::optional

namespace heron
{
    // Function to compute the ray-plane intersection
    static std::optional<Vector3f> RayPlaneIntersection(
        const Vector3f &r0, // Ray origin
        const Vector3f &rd, // Ray direction (normalized)
        const Vector3f &c,  // Quad center (a point on the plane)
        const Vector3f &n   // Plane normal (normalized)
    )
    {
        double denominator = rd.dot(n);
        if (std::abs(denominator) < 1e-6)
            return std::nullopt; // No intersection
        double t = (c - r0).dot(n) / denominator;
        if (t < 0)
            return std::nullopt; // Intersection behind the ray origin
        return r0 + t * rd;
    }

    // Function to compute the 2D coordinate on the quad's plane
    static std::optional<Vector2f> Quad2DCoordinate(
        const Vector3f &p, // Intersection point
        const Vector3f &c, // Quad center
        const Vector3f &u, // Quad basis vector 1 (normalized)
        const Vector3f &v  // Quad basis vector 2 (normalized)
    )
    {
        Vector3f r = p - c;
        double x = r.dot(u);
        double y = r.dot(v);
        return Eigen::Vector2f(x, y);
    }

    static std::optional<Vector2f> EyeRayQuadIntersect2DCoord(const Transform &head_transform,
                                                              const Transform &recenter_transform,
                                                              const Transform &quad_transform,
                                                              const Transform &recentered_quad_transform)
    {
        // PrintObject("head_pose:", head_transform);
        Vector3f r0 = recenter_transform.position;
        Vector3f rd = head_transform.rotation * Vector3f(0, 0, -1);
        Vector3f n = r0 - recentered_quad_transform.position;
        Vector3f x_base = (recenter_transform.rotation * (Vector3f(1.0f, 0.0f, 0.0f) + quad_transform.position) + recenter_transform.position) - recentered_quad_transform.position;
        Vector3f y_base = (recenter_transform.rotation * (Vector3f(0.0f, 1.0f, 0.0f) + quad_transform.position) + recenter_transform.position) - recentered_quad_transform.position;
        if (auto intersection = RayPlaneIntersection(r0, rd.normalized(), recentered_quad_transform.position, n.normalized()))
        {
            // PrintObject("intersection 3D:", *intersection);
            return Quad2DCoordinate(*intersection, recentered_quad_transform.position, x_base.normalized(), y_base.normalized());
        }
        // HERON_LOG_TRACE("no intersection with quad plane");
        return std::nullopt; // Intersection behind the ray origin
    }

    void IsLookingAt(bool &inner, bool &outer, float fov_up, float fov_down, float fov_sides,
                     const Transform &head_transform,
                     const Transform &recenter_transform,
                     const Transform &quad_transform,
                     const Transform &recentered_quad_transform)
    {
        inner = false;
        outer = false;
        auto coord_2D = EyeRayQuadIntersect2DCoord(head_transform, recenter_transform, quad_transform, recentered_quad_transform);
        if (!coord_2D)
            return;
        // HERON_LOG_TRACE("intersection 2D:{}, {}", (*coord_2D).x(), (*coord_2D).y());
        Vector2f coord_2D_at_1m = *coord_2D / (-quad_transform.position.z());
        float v_angle = atan(coord_2D_at_1m.y()) / M_PI * 180.0;
        float full_angle = atan(coord_2D_at_1m.norm()) / M_PI * 180.0;
        float fov_up_inner = fov_up - 1.5f;
        float fov_down_inner = fov_down + 1.5f;
        float fov_sides_inner = fov_sides - 2.0f;
        float fov_up_outer = fov_up + 1.0f;
        float fov_down_outer = fov_down - 1.0f;
        float fov_sides_outer = fov_sides + 1.5f;
        inner = v_angle < fov_up_inner && v_angle > fov_down_inner && full_angle < fov_sides_inner;
        outer = v_angle < fov_up_outer && v_angle > fov_down_outer && full_angle < fov_sides_outer;
        // HERON_LOG_TRACE("intersection v_angle:{} full_angle:{} inner:{} outer:{}", v_angle, full_angle, inner, outer);
    }

    void WorldToHomogeneous(const Vector3f world_3d_coord, const Transform &head_pose, const Mat4f &projection, Vector4f &homogeneous)
    {
        Vector3f tmp = head_pose.rotation.inverse() * (world_3d_coord - head_pose.position);
        Vector4f tmp4(tmp.x(), tmp.y(), tmp.z(), 1.0f);
        homogeneous = projection * tmp4;
    }

    // 判断一个点是否在固定矩形内（假设矩形为 (0,0)-(target_rect_width,target_rect_height)）
    bool PointInRect(const Eigen::Vector2f &pt, const Vector2i &target_rect_size)
    {
        return (pt.x() >= 0 && pt.x() <= target_rect_size.x() && pt.y() >= 0 && pt.y() <= target_rect_size.y());
    }

    // 判断两条线段是否相交
    static bool SegmentsIntersect(const Eigen::Vector2f &p1, const Eigen::Vector2f &p2,
                                  const Eigen::Vector2f &q1, const Eigen::Vector2f &q2)
    {
        auto orientation = [](const Eigen::Vector2f &a, const Eigen::Vector2f &b, const Eigen::Vector2f &c) -> float
        {
            // 计算叉乘结果
            return (b.y() - a.y()) * (c.x() - b.x()) - (b.x() - a.x()) * (c.y() - b.y());
        };

        float o1 = orientation(p1, p2, q1);
        float o2 = orientation(p1, p2, q2);
        float o3 = orientation(q1, q2, p1);
        float o4 = orientation(q1, q2, p2);

        // 如果线段 p 和 q 在每个方向上的“转向”不同，则有交点
        if (o1 * o2 < 0 && o3 * o4 < 0)
            return true;
        return false;
    }

    // 利用 SAT 检测两个多边形是否有交集
    // 此处假设 poly1 和 poly2 的顶点均按顺时针或逆时针顺序排列
    static bool PolygonOverlapSAT(const std::vector<Eigen::Vector2f> &poly1,
                                  const std::vector<Eigen::Vector2f> &poly2)
    {
        // 辅助 lambda：计算多边形每条边的单位法向量
        auto getAxes = [](const std::vector<Eigen::Vector2f> &poly) -> std::vector<Eigen::Vector2f>
        {
            std::vector<Eigen::Vector2f> axes;
            size_t n = poly.size();
            for (size_t i = 0; i < n; ++i)
            {
                Eigen::Vector2f edge = poly[(i + 1) % n] - poly[i];
                Eigen::Vector2f axis(-edge.y(), edge.x());
                if (axis.norm() != 0)
                    axis.normalize();
                axes.push_back(axis);
            }
            return axes;
        };

        // 获取所有候选分离轴
        std::vector<Eigen::Vector2f> axes;
        auto axes1 = getAxes(poly1);
        auto axes2 = getAxes(poly2);
        axes.insert(axes.end(), axes1.begin(), axes1.end());
        axes.insert(axes.end(), axes2.begin(), axes2.end());

        // 对每个候选轴检测投影是否存在分离
        for (const auto &axis : axes)
        {
            float min1 = std::numeric_limits<float>::max();
            float max1 = -std::numeric_limits<float>::max();
            for (const auto &p : poly1)
            {
                float proj = p.dot(axis);
                min1 = std::min(min1, proj);
                max1 = std::max(max1, proj);
            }
            float min2 = std::numeric_limits<float>::max();
            float max2 = -std::numeric_limits<float>::max();
            for (const auto &p : poly2)
            {
                float proj = p.dot(axis);
                min2 = std::min(min2, proj);
                max2 = std::max(max2, proj);
            }
            // 如果在当前轴上投影没有重叠，则说明有分离轴
            if (max1 < min2 || max2 < min1)
                return false;
        }
        return true;
    }

    // 检测输入的点集（可能是点、线段或者多边形，且已按顺序排列）与固定矩形的重叠情况
    static bool OverlapWithRect(const std::vector<Eigen::Vector2f> &pts, const Vector2i &target_rect_size)
    {
        // 固定矩形的顶点（轴对齐矩形）
        std::vector<Eigen::Vector2f> rectA = {
            Eigen::Vector2f(0, 0),
            Eigen::Vector2f(target_rect_size.x(), 0),
            Eigen::Vector2f(target_rect_size.x(), target_rect_size.y()),
            Eigen::Vector2f(0, target_rect_size.y())};

        if (pts.empty())
            return false;

        if (pts.size() == 1)
        {
            // 只有一个点，直接判断该点是否在矩形内
            return PointInRect(pts[0], target_rect_size);
        }
        else if (pts.size() == 2)
        {
            // 两个点形成线段：如果任一端点在矩形内，则认为有重叠
            if (PointInRect(pts[0], target_rect_size) || PointInRect(pts[1], target_rect_size))
                return true;
            // 否则，判断线段是否与矩形的任一边相交
            std::vector<std::pair<Eigen::Vector2f, Eigen::Vector2f>> rectEdges = {
                {rectA[0], rectA[1]},
                {rectA[1], rectA[2]},
                {rectA[2], rectA[3]},
                {rectA[3], rectA[0]}};
            for (const auto &edge : rectEdges)
            {
                if (SegmentsIntersect(pts[0], pts[1], edge.first, edge.second))
                    return true;
            }
            return false;
        }
        else
        {
            // 至少3个点构成多边形，直接用 SAT 判断多边形与固定矩形是否有交集
            return PolygonOverlapSAT(pts, rectA);
        }
    }

    bool CanvasProjectionOutOfScreen(const Vector4f &tl, const Vector4f &tr, const Vector4f &bl, const Vector4f &br, const Vector2i &screen_size_pixel,
                                     Vector2f &tl_screen, Vector2f &tr_screen, Vector2f &bl_screen, Vector2f &br_screen, bool &tl_in, bool &tr_in, bool &bl_in, bool &br_in)
    {
        // XXX: pay attention to the abs(corner.w()) in the following code
        tl_screen = Vector2f((tl.x() / abs(tl.w()) + 1.0f) / 2.0f * screen_size_pixel.x(), (tl.y() / abs(tl.w()) + 1.0f) / 2.0f * screen_size_pixel.y());
        tr_screen = Vector2f((tr.x() / abs(tr.w()) + 1.0f) / 2.0f * screen_size_pixel.x(), (tr.y() / abs(tr.w()) + 1.0f) / 2.0f * screen_size_pixel.y());
        bl_screen = Vector2f((bl.x() / abs(bl.w()) + 1.0f) / 2.0f * screen_size_pixel.x(), (bl.y() / abs(bl.w()) + 1.0f) / 2.0f * screen_size_pixel.y());
        br_screen = Vector2f((br.x() / abs(br.w()) + 1.0f) / 2.0f * screen_size_pixel.x(), (br.y() / abs(br.w()) + 1.0f) / 2.0f * screen_size_pixel.y());
        tl_in = (tl.w() > 0) && PointInRect(tl_screen, screen_size_pixel);
        tr_in = (tr.w() > 0) && PointInRect(tr_screen, screen_size_pixel);
        bl_in = (bl.w() > 0) && PointInRect(bl_screen, screen_size_pixel);
        br_in = (br.w() > 0) && PointInRect(br_screen, screen_size_pixel);
        return (!OverlapWithRect({tl_screen, tr_screen, br_screen, bl_screen}, screen_size_pixel)) || (tl.w() < 0 && tr.w() < 0 && bl.w() < 0 && br.w() < 0);
    }

    static int s_get_size_factor_count = 0;
    bool GetCanvasProjectionSizeFactor(const Vector4f &tl, const Vector4f &tr, const Vector4f &bl, const Vector4f &br,
                                       const Vector2i &screen_size_pixel, Vector2f &size_factor, bool &h_factor_valid, bool &v_factor_valid)
    {
        s_get_size_factor_count++;
        h_factor_valid = true;
        v_factor_valid = true;
        Vector2f tl_screen, tr_screen, bl_screen, br_screen;
        bool tl_in, tr_in, bl_in, br_in;
        if (CanvasProjectionOutOfScreen(tl, tr, bl, br, screen_size_pixel, tl_screen, tr_screen, bl_screen, br_screen, tl_in, tr_in, bl_in, br_in))
        {
            size_factor.x() = 0.0f;
            size_factor.y() = 0.0f;
            return false;
        }
        float tl_tr_h = (tl_screen - tr_screen).norm() / screen_size_pixel.x();
        float bl_br_h = (bl_screen - br_screen).norm() / screen_size_pixel.x();
        float tl_bl_v = (tl_screen - bl_screen).norm() / screen_size_pixel.y();
        float tr_br_v = (tr_screen - br_screen).norm() / screen_size_pixel.y();
        size_factor.x() = (tl_tr_h + bl_br_h) / 2.0f;
        size_factor.y() = (tl_bl_v + tr_br_v) / 2.0f;
        int valid_corner_count = tl_in + tr_in + bl_in + br_in;
        if (s_get_size_factor_count % 100 == 0)
        {
            // HERON_LOG_TRACE("corners tl({:.1f},{:.1f}), tr({:.1f},{:.1f}), bl({:.1f},{:.1f}, br({:.1f},{:.1f}))", tl_screen.x(), tl_screen.y(), tr_screen.x(), tr_screen.y(), bl_screen.x(), bl_screen.y(), br_screen.x(), br_screen.y());
            // HERON_LOG_TRACE("{} corners visiable tl:{} tr:{} br:{} bl:{} h1:{} h2:{} v1:{} v2:{}", valid_corner_count, tl_in, tr_in, bl_in, br_in, tl_tr_h, bl_br_h, tl_bl_v, tr_br_v);
        }
        // valid count = 1 and valid count = 3 are "conjugate"
        if ((valid_corner_count == 1 && tl_in) || (valid_corner_count == 3 && !br_in))
        {
            size_factor.x() = tl_tr_h;
            size_factor.y() = tl_bl_v;
        }
        if ((valid_corner_count == 1 && tr_in) || (valid_corner_count == 3 && !bl_in))
        {
            size_factor.x() = tl_tr_h;
            size_factor.y() = tr_br_v;
        }
        if ((valid_corner_count == 1 && bl_in) || (valid_corner_count == 3 && !tr_in))
        {
            size_factor.x() = bl_br_h;
            size_factor.y() = tl_bl_v;
        }
        if ((valid_corner_count == 1 && br_in) || (valid_corner_count == 3 && !tl_in))
        {
            size_factor.x() = bl_br_h;
            size_factor.y() = tr_br_v;
        }
        if (valid_corner_count == 2)
        {
            if (tl_in && tr_in)
                size_factor.x() = tl_tr_h;
            if (bl_in && br_in)
                size_factor.x() = bl_br_h;
            if (tl_in && bl_in)
                size_factor.y() = tl_bl_v;
            if (tr_in && br_in)
                size_factor.y() = tr_br_v;
        }
        return true;
    }

    static int s_get_bad_view_count = 0;
    void IsBadView6dofTest(const Transform &quad_transform, const Transform &head_pose, float quad_size_meters, bool &very_bad, bool &bad)
    {
        s_get_bad_view_count++;
        Vector3f quad_center_in_world = quad_transform.rotation * Vector3f(0.0f, 0.0f, 0.0f) + quad_transform.position;
        Vector3f eye_to_quad_center = quad_center_in_world - head_pose.position;
        Vector3f quad_normal_in_world = quad_transform.rotation * Vector3f(0, 0, 1);
        float angle_radius = acos(eye_to_quad_center.normalized().dot(quad_normal_in_world));
        very_bad = abs(angle_radius - M_PI / 2) < 0.04f && eye_to_quad_center.norm() > quad_size_meters;
        bad = abs(angle_radius - M_PI / 2) < 0.05f && eye_to_quad_center.norm() > (quad_size_meters + 0.15f);
        if (s_get_bad_view_count % 100 == 0)
        {
            // HERON_LOG_TRACE("bad:{} very_bad:{} angle_degree:{} distance:{} eye_ray:({:.2f},{:.2f},{:.2f}) quad_normal:({:.2f},{:.2f},{:.2f})",
            //                 bad, very_bad, angle_radius / M_PI * 180.0, eye_to_quad_center.norm(),
            //                 eye_to_quad_center.x(), eye_to_quad_center.y(), eye_to_quad_center.z(),
            //                 quad_normal_in_world.x(), quad_normal_in_world.y(), quad_normal_in_world.z());
        }
    }

    // Function to compute the distance between a point(p) and a line(direction vector v and point a)
    static float PointToLineDistance(const Vector3f &p, const Vector3f &a, const Vector3f &v)
    {
        Vector3f ap = p - a;            // Vector from A to P
        Vector3f cross = ap.cross(v);   // Cross product
        return cross.norm() / v.norm(); // Distance formula
    }

    void IsInCylinder(const Transform &recenter_transform, const Transform &head_pose, float radius, bool &inner, bool &outer)
    {
        float distance = PointToLineDistance(head_pose.position, recenter_transform.position, recenter_transform.rotation * Vector3f(0, 1, 0));
        inner = distance < (radius - 0.15f);
        outer = distance < (radius - 0.05f);
        // HERON_LOG_TRACE("IsInCylinder distance:{:.2f} r:{:.2f} {} {}", distance, radius, inner, outer);
    }

    std::pair<Vector2f, Vector2f> ComputeBoundingBox(const std::vector<Vector2f> &points)
    {
        Vector2f tl(std::numeric_limits<float>::max(), std::numeric_limits<float>::max());
        Vector2f br(-std::numeric_limits<float>::max(), -std::numeric_limits<float>::max());
        for (const auto &point : points)
        {
            tl.x() = std::min(tl.x(), point.x());
            tl.y() = std::min(tl.y(), point.y());
            br.x() = std::max(br.x(), point.x());
            br.y() = std::max(br.y(), point.y());
        }
        return {tl, br};
    }

    // Computes the overlapping area between two axis aligned bounding boxes.
    // Each bounding box is given as a pair: {minCorner, maxCorner}.
    float ComputeOverlapArea(const std::pair<Vector2f, Vector2f> &box1,
                             const std::pair<Vector2f, Vector2f> &box2)
    {
        // Compute the overlap in the x-dimension
        float overlapWidth = std::max(0.0f, std::min(box1.second.x(), box2.second.x()) -
                                                std::max(box1.first.x(), box2.first.x()));
        // Compute the overlap in the y-dimension
        float overlapHeight = std::max(0.0f, std::min(box1.second.y(), box2.second.y()) -
                                                 std::max(box1.first.y(), box2.first.y()));
        // If either dimension has no overlap, the area is 0
        return overlapWidth * overlapHeight;
    }

    // Constants
    static const float _EPS = std::numeric_limits<float>::epsilon() * 4.0;

    // Axis sequences for Euler angles
    static std::array<int, 4> _NEXT_AXIS = {1, 2, 0, 1};

    // Map axes strings to/from tuples of inner axis, parity, repetition, frame
    static std::map<std::string, std::tuple<int, int, int, int>> _AXES2TUPLE = {
        {"sxyz", {0, 0, 0, 0}}, {"sxyx", {0, 0, 1, 0}}, {"sxzy", {0, 1, 0, 0}}, {"sxzx", {0, 1, 1, 0}}, {"syzx", {1, 0, 0, 0}}, {"syzy", {1, 0, 1, 0}}, {"syxz", {1, 1, 0, 0}}, {"syxy", {1, 1, 1, 0}}, {"szxy", {2, 0, 0, 0}}, {"szxz", {2, 0, 1, 0}}, {"szyx", {2, 1, 0, 0}}, {"szyz", {2, 1, 1, 0}}, {"rzyx", {0, 0, 0, 1}}, {"rxyx", {0, 0, 1, 1}}, {"ryzx", {0, 1, 0, 1}}, {"rxzx", {0, 1, 1, 1}}, {"rxzy", {1, 0, 0, 1}}, {"ryzy", {1, 0, 1, 1}}, {"rzxy", {1, 1, 0, 1}}, {"ryxy", {1, 1, 1, 1}}, {"ryxz", {2, 0, 0, 1}}, {"rzxz", {2, 0, 1, 1}}, {"rxyz", {2, 1, 0, 1}}, {"rzyz", {2, 1, 1, 1}}};

    Eigen::Quaternionf QuaternionFromEuler(float ai, float aj, float ak, const std::string &axes)
    {
        // Extract axis sequence
        int firstaxis, parity, repetition, frame;
        try
        {
            std::tie(firstaxis, parity, repetition, frame) = _AXES2TUPLE.at(axes);
        }
        catch (const std::out_of_range &)
        {
            HERON_LOG_ERROR("{} Invalid axis sequence", __FUNCTION__);
            return Eigen::Quaternionf::Identity();
        }

        int i = firstaxis;
        int j = _NEXT_AXIS[i + parity];
        int k = _NEXT_AXIS[i - parity + 1];

        if (frame)
        {
            std::swap(ai, ak);
        }
        if (parity)
        {
            aj = -aj;
        }

        ai /= 2.0;
        aj /= 2.0;
        ak /= 2.0;

        float ci = std::cos(ai);
        float si = std::sin(ai);
        float cj = std::cos(aj);
        float sj = std::sin(aj);
        float ck = std::cos(ak);
        float sk = std::sin(ak);
        float cc = ci * ck;
        float cs = ci * sk;
        float sc = si * ck;
        float ss = si * sk;

        Eigen::Quaternionf quaternion;
        if (repetition)
        {
            quaternion.coeffs()(i) = cj * (cs + sc);
            quaternion.coeffs()(j) = sj * (cc + ss);
            quaternion.coeffs()(k) = sj * (cs - sc);
            quaternion.w() = cj * (cc - ss);
        }
        else
        {
            quaternion.coeffs()(i) = cj * sc - sj * cs;
            quaternion.coeffs()(j) = cj * ss + sj * cc;
            quaternion.coeffs()(k) = cj * cs - sj * sc;
            quaternion.w() = cj * cc + sj * ss;
        }

        if (parity)
        {
            quaternion.coeffs()(j) *= -1;
        }

        return quaternion;
    }

    std::tuple<float, float, float> EulerFromMatrix(const Eigen::Matrix3f &matrix, const std::string &axes)
    {
        // Extract axis sequence
        int firstaxis, parity, repetition, frame;
        try
        {
            std::tie(firstaxis, parity, repetition, frame) = _AXES2TUPLE.at(axes);
        }
        catch (const std::out_of_range &)
        {
            HERON_LOG_ERROR("{} Invalid axis sequence", __FUNCTION__);
            return std::make_tuple(0.0, 0.0, 0.0);
        }

        int i = firstaxis;
        int j = _NEXT_AXIS[i + parity];
        int k = _NEXT_AXIS[i - parity + 1];

        Eigen::Matrix3f M = matrix;
        float ax, ay, az;

        if (repetition)
        {
            float sy = std::sqrt(M(i, j) * M(i, j) + M(i, k) * M(i, k));
            if (sy > _EPS)
            {
                ax = std::atan2(M(i, j), M(i, k));
                ay = std::atan2(sy, M(i, i));
                az = std::atan2(M(j, i), -M(k, i));
            }
            else
            {
                ax = std::atan2(-M(j, k), M(j, j));
                ay = std::atan2(sy, M(i, i));
                az = 0.0;
            }
        }
        else
        {
            float cy = std::sqrt(M(i, i) * M(i, i) + M(j, i) * M(j, i));
            if (cy > _EPS)
            {
                ax = std::atan2(M(k, j), M(k, k));
                ay = std::atan2(-M(k, i), cy);
                az = std::atan2(M(j, i), M(i, i));
            }
            else
            {
                ax = std::atan2(-M(j, k), M(j, j));
                ay = std::atan2(-M(k, i), cy);
                az = 0.0;
            }
        }

        // Adjust signs based on parity and frame
        if (parity)
        {
            ax = -ax;
            ay = -ay;
            az = -az;
        }
        if (frame)
        {
            std::swap(ax, az);
        }

        return std::make_tuple(ax, ay, az);
    }

    static float CrossNorm(Vector2f p1, Vector2f p2)
    {
        return std::abs(p1.x() * p2.y() - p1.y() * p2.x());
    }

    // 顶点顺序 左上 右上 左下 右下
    static void GenGDCWeightForGrid(std::vector<Vector2f> points, Vector2f srcPoint, std::vector<float> &weights)
    {
        int indexA_1 = 1;
        int indexA_2 = 2;
        int indexB = 0;
        int indexC = 3;

        double minX = points[0].x();
        double maxX = points[1].x();
        double minY = points[0].y();
        double maxY = points[2].y();
        double xScale = (srcPoint.x() - minX) / (maxX - minX);
        double yScale = (srcPoint.y() - minY) / (maxY - minY);
        Vector2f pB(points[indexB].x(), points[indexB].y());
        Vector2f pC(points[indexC].x(), points[indexC].y());
        Vector2f pP(srcPoint.x(), srcPoint.y());
        if (yScale > xScale)
        {
            // 三角形的面积是两个向量叉乘的绝对值的一半（右手系为正）
            Vector2f pA(points[indexA_2].x(), points[indexA_2].y());
            double S = CrossNorm(pB - pA, pC - pA);
            double kA = CrossNorm(pB - pP, pC - pP) / S;
            double kB = CrossNorm(pA - pP, pC - pP) / S;
            double kC = CrossNorm(pA - pP, pB - pP) / S;
            // weights[indexB] = kB;
            // weights[indexA_1] = 0;
            // weights[indexA_2] = kA;
            // weights[indexC] = kC;
            weights[indexB] = kB;
            weights[indexA_1] = kA;
            weights[indexA_2] = 0;
            weights[indexC] = kC;
        }
        else
        {
            Vector2f pA(points[indexA_1].x(), points[indexA_1].y());
            double S = CrossNorm(pB - pA, pC - pA);
            double kA = CrossNorm(pB - pP, pC - pP) / S;
            double kB = CrossNorm(pA - pP, pC - pP) / S;
            double kC = CrossNorm(pA - pP, pB - pP) / S;
            // weights[indexB] = kB;
            // weights[indexA_1] = kA;
            // weights[indexA_2] = 0;
            // weights[indexC] = kC;
            weights[indexB] = kB;
            weights[indexA_1] = 0;
            weights[indexA_2] = kA;
            weights[indexC] = kC;
        }
    }

    void GenGDCWeight(int grid_height, int grid_width, float *weights)
    {
        std::vector<Vector2f> gridPoint4;
        gridPoint4.push_back(Vector2f(0, 0));
        gridPoint4.push_back(Vector2f(grid_width, 0));
        gridPoint4.push_back(Vector2f(0, grid_height));
        gridPoint4.push_back(Vector2f(grid_width, grid_height));
        std::vector<float> weight_for_grid(4);
        for (int i = 0; i < grid_height; i++)
        { // x = i y = j
            for (int j = 0; j < grid_width; j++)
            {
                Vector2f src(i, j);
                GenGDCWeightForGrid(gridPoint4, src, weight_for_grid);
                weights[(i * grid_height + j) * 4] = weight_for_grid[0];
                weights[(i * grid_height + j) * 4 + 1] = weight_for_grid[1];
                weights[(i * grid_height + j) * 4 + 2] = weight_for_grid[2];
                weights[(i * grid_height + j) * 4 + 3] = weight_for_grid[3];
            }
        }
    }
} // namespace heron