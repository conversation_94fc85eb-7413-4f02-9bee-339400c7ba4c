#include <heron/interface_provider/hmd.h>
#include <heron/interface_provider/common_macro.h>
#include <heron/util/nr_type_converter.h>
#include <heron/interface/include/common/nr_plugin_hmd.h>
#include <heron/util/log.h>

#include <framework/util/plugin_util.h>

extern NRPluginHandle heron_g_handle;

static std::unique_ptr<NRHMDInterface> s_interface = nullptr;

namespace heron::interface_provider
{
    void HMDInterface::GetInterfaceInstance(void *interfaces)
    {
        s_interface = framework::util::GetInterface<NRHMDInterface>(static_cast<NRInterfaces *>(interfaces));
        if (!s_interface)
        {
            fprintf(stderr, "error on plugin load: get %s fail. going to abort", "NRHMDInterface");
            usleep(500 * 1000);
            std::abort();
        }
    }

    bool HMDInterface::GetComponentFov(Component component, Fov4f *out_fov)
    {
        NRFov4f out_nr_fov4f;
        CALL_INTERFACE_API(NRHMDInterface, GetComponentFov, true, (NRComponent)component, &out_nr_fov4f);
        ConvertToFov4f(*out_fov, out_nr_fov4f);
        return true;
    }

    bool HMDInterface::GetComponentResolution(Component component, Vector2i *out_resolution)
    {
        NRSize2i out_nr_resolution;
        CALL_INTERFACE_API(NRHMDInterface, GetComponentResolution, true, (NRComponent)component, &out_nr_resolution);
        ConvertToVector2i(*out_resolution, out_nr_resolution);
        return true;
    }

    bool HMDInterface::GetComponentRefreshRate(Component component, uint32_t *out_refresh_rate)
    {
        CALL_INTERFACE_API(NRHMDInterface, GetComponentRefreshRate, true, (NRComponent)component, out_refresh_rate);
        return true;
    }

    bool HMDInterface::GetComponentIntrinsic(Component component, Mat3f *out_intrinsic)
    {
        NRMat3f out_nr_intrinsic;
        CALL_INTERFACE_API(NRHMDInterface, GetComponentIntrinsic, true, (NRComponent)component, &out_nr_intrinsic);
        ConvertToMat3f(*out_intrinsic, out_nr_intrinsic);
        return true;
    }

    bool HMDInterface::GetComponentPoseFromHead(
        Component component,
        Transform *out_transform)
    {
        NRTransform out_nr_transform;
        CALL_INTERFACE_API(NRHMDInterface, GetComponentPoseFromHead, true, (NRComponent)component, &out_nr_transform);
        NRTransformToTransform(*out_transform, out_nr_transform);
        return true;
    }

    bool HMDInterface::GetComponentExtrinsic(
        Component base_component,
        Component target_component,
        Transform *out_extrinsic)
    {
        NRTransform out_nr_transform;
        CALL_INTERFACE_API(NRHMDInterface, GetComponentExtrinsic, true, (NRComponent)base_component, (NRComponent)target_component, &out_nr_transform);
        NRTransformToTransform(*out_extrinsic, out_nr_transform);
        return true;
    }
    bool HMDInterface::GetComponentDisplayDistortionSize(
        Component component,
        Vector2i *distortion_size)
    {
        NRSize2i out_distortion_size;
        CALL_INTERFACE_API(NRHMDInterface, GetComponentDisplayDistortionSize, true, (NRComponent)component, &out_distortion_size);
        ConvertToVector2i(*distortion_size, out_distortion_size);
        return true;
    }
    bool HMDInterface::GetComponentDisplayDistortionData(
        Component component,
        int size,
        float *data)
    {
        CALL_INTERFACE_API(NRHMDInterface, GetComponentDisplayDistortionData, true, (NRComponent)component, size, data);
        return true;
    }
} // namespace heron::interface_provider