{
    "flinger": {
        "log_level": "2", // 越高打印越少，1:打印Trace及以上级别；2:打印Debug及以上级别；3：打印Info及以上级别
        "debug_log": { // 指定功能相关log开关
            "suitable_src_frame_size": false, // 是否打印调整src_frame_size请求的log
            "focus_judger": false, // 是否打印focus_judger debug日志
            "warp_job_block_id": false, // 是否打印每个中断到来时，进行warp计算的block id
            "embedded_metadata": false, // 是否打印埋在src_frame中的metadata，间隔print_frame_interval帧打印一次
            "embedded_metadata_timing": false, // 是否打印埋在src_frame中的metadata相关的timing信息,每帧都打印
            "msg_metadata": false, // 是否打印从msg中解析出来的metadata, 间隔print_frame_interval帧打印一次
            "populated_metadata": false, // 是否打印填充到frame_info中的metadata, 间隔print_frame_interval帧打印一次
            "timing_detail": false, // 是否打印详细的timing统计信息，间隔print_frame_interval帧打印一次
            "timing_verbose": false, // 是否打印详细的timing统计信息，每帧都打印
            "frame_data_buffer_status": false, // 是否打印frame data buffer的占用状态
            "dp_frame_data_detail": false, // 是否打印ReceiveFrame线程get/release的帧首物理地址
            "gdc_configured_frame_data_detail": false, // 是否打印配置给GDC的帧首物理地址
            "quad_status_interpolation": false, // 打印投屏模式下画布距离，尺寸的插值动画过程调试信息
            "target_block_id": false, // 打印每个中断到来时，进行warp计算的block id
            "head_pose": false, // 间隔print_interrupt_interval打印一次头部姿态信息(GetDevicePose接口的出参)
            "osd_submit": false // 是否打印osd提交相关的log
        },
        "no_sys_log_when_sending_tcp_log": false, // 发送TCP log时,关闭sys_log（pilot.log）打印
        "dp_video_pipeline_param": { // 忽略实际dp_src和眼镜屏幕的分辨率，强制使用配置中的数据
            "screen_width": 1920, // 单眼屏幕宽
            "screen_height": 1080, // 单眼屏幕高
            "src_width": 1920, // host dp数据的宽
            "src_height": 1080 // host dp数据的高
        },
        "hide_metadata_lines": false, // GDC是否隐藏nebula埋的数据行
        "no_update_matrix_buffer": false, // 正常进行所有warp计算，但是不更新gdc matrix buffer。使用debug_warp_matrix_file_path文件中的矩阵数据（读文件错误则用默认的单位矩阵）
        "mesh_only_warp": false, // 仅使用mesh进行warp，不使用warp矩阵
        "debug_warp_matrix_file_path": "/usrdata/data-6-left/warp_cmodel.dat", // disable_warp=true时，使用的指定矩阵bin文件，若文件不存在，则使用单位矩阵
        "pose_timestamp_offset_ns": 0, // warp的时间戳的偏移量，单位：ns
        "always_do_pupil_adjust": true, // 瞳距调节档位0时，是否调用瞳距调节接口设置BaseQuad；不调用则按照内参的最大FOV设置BaseQuad
        "ignore_space_status_validation": false, // 忽略space_status的合法性检查
        "force_start_dp_render": false, // 是否强制启动dp渲染
        "dp_frame_info_count": 3, // dp_frame_info缓存的帧数
        "arbitrary_vo_fps": false, // 设为true后，则VO/GDC的timing强制按照default_vo_fps进行计算
        "default_vo_fps": 90.0, // 默认眼镜屏幕的刷新率
        "target_src_size_pixel": [
            1920,
            1080
        ], // 强制使用dpisp downscaler将输入源采样到target_src_size_pixel
        "init_line_cnt": 400, // VI数据触发VO启动的行数
        "reset_dp_display_in_bypass_mode": false, // 是否在bypass模式下，重置dp_display
        "dp_submit_line_count": 10, // single_buffer时，DpGetFrame一帧写完N行后再返回
        "dp_display_sync_line_count": 64, // single_buffer时，VI数据触发VO启动的行数
        "sleep_us_after_dp_rx_done": 0, // ReceiveFrame中DpGetFrame()返回后sleep的时间，单位：us
        "use_async_warp": false, // 是否另开一个线程做warp（不开启则直接在vo_sub线程中做warp计算）
        "use_host_ptw_warp": false, // 在有WITH_NEBULA的情况下是否使用ptw warp
        "only_use_left_vo_callback": false, // 是否只使用左display的callback,在左display的callback中做双眼warp
        "disable_warp_at_0DOF": false, // 为true时，在云台不防抖模式下，没有畸变矫正，镜头内、外参等warp的效果
        "check_underflow": false, // 设为true时，应用层会检查underflow（underflow标记寄存器是“读复位”的，所以会影响底层检查underflow的逻辑）
        "ignore_linebuffer_reset": true, // 忽略回调中的need_reset标记
        "dump_dp_input_config": { // dump dp_in 数据配置，以裸字节流的形式保存到文件
            "dump_dp_src_frame": false, // 是否dump 整帧数据
            "dump_whole_frame_interval": 100, // 每多少帧dump一次
            "dump_first_lines": false, // 是否dump 前几行数据
            "dump_first_lines_frame_interval": 100, // 每多少帧dump一次
            "line_count": 10, // 每次dump前多少行
            "width": 100 // 每行截取多少个字节
        },
        "warp_timing": {
            "warp_delay_block_cnt": 4,
            "overwrite_block_cnt": 0,
            "warp_block_cnt_at_each_callback": 8,
            "get_frame_before_special": 0
        },
        "ultra_wide_timing": {
            "warp_delay_block_cnt": 12,
            "overwrite_block_cnt": 0,
            "warp_block_cnt_at_each_callback": 4,
            "get_frame_before_special": 0
        },
        "with_nebula_timing": {
            "warp_delay_block_cnt": 20,
            "overwrite_block_cnt": 0,
            "warp_block_cnt_at_each_callback": 4,
            "get_frame_before_special": 0
        },
        "print_frame_interval": 1000, // 每多少帧打印一次帧相关debug信息,0表示不打印
        "print_interrupt_interval": 1000, // 每多少中断印一次中断相关debug信息，0表示不打印
        "profile_callback_interval": false, // 是否统计相邻callback的时间间隔
        "profile_atw": { // 是否统计ptw相关的性能信息，包含line_diff等
            "line_start": -400, //计入统计的最小line_diff
            "line_step": 50, //统计line_diff分布直方图的步长
            "bar_count": 16, //统计line_diff分布直方图的区间个数
            "duration_seconds": 10 //统计line_diff分布直方图时长
        },
        "enable_atw_detail_collection": false, // 是否开启ATW details的统计
        "atw_details_dump_dir": "/usrdata/log/current_log_dir", // ATW details dump的目录
        "dump_frame_head_transforms": false, // 是否每间隔一段时间dump一次一整帧的头部姿态
        "max_atw_details_size": 1200, // ATW details的缓存帧数。统计时，超过该帧数时，会dump到文件
        "lines_64_enable": true, // 是否开启video_layer64行linebuffer是默认32行的2倍）
        "gdc_left": {
            "use_sram": false, // 设置为true时，将GDC的mesh，warp，weight表地址映射到sram
            "start_mode": 3, // 2 shadow mode; 3 free run mode
            "warp_mode": 0, // 0: use warp.dat file; 1 use apb matrix; 2 disable。默认用0。填2，关闭外参、头部姿态
            "mesh_mode": 0, // 0:32x32 blocking; 3:disable。我们用0
            "use_identity_mesh": false, // 用“identity“的mesh数据
            "weight_mode": 1, // 0 inner bi-linear;1 use external file; 我们用1
            "ext_padding": { // GDC采样区域外的颜色，【180，80，240】为橙色，可以这样填，方便确认display是否处于预期工作状态
                "c0": 180,
                "c1": 80,
                "c2": 240
            }
        },
        "gdc_right": {
            "use_sram": false, // 设置为true时，将GDC的mesh，warp，weight表地址映射到sram
            "start_mode": 3, // 2 shadow mode; 3 free run mode
            "warp_mode": 0, // 0: use warp.dat file; 1 use apb matrix; 2 disable。默认用0。填2，关闭外参、头部姿态
            "mesh_mode": 0, // 0:32x32 blocking; 3:disable。我们用0
            "use_identity_mesh": false, // 用“identity“的mesh数据
            "weight_mode": 1, // 0 inner bi-linear;1 use external file; 我们用1
            "ext_padding": { // GDC采样区域外的颜色，【180，80，240】为橙色，可以这样填，方便确认display是否处于预期工作状态
                "c0": 180,
                "c1": 80,
                "c2": 240
            }
        },
        "immediate_return_on_vo_callback": false, // 收到所有VO回调都立即返回
        "use_perception_recenter": false, // 触发recenter时，触发工程内部的recenter后，再调用一下perception的recenter方法
        "gen_fake_vsync": false, // 是否在NRDpGetFrame返回后生成假vsync信号上报给host
        "mmz_cached_for_warp_matrices": false, // GDC warp表是否使用cached mmz。对应BSP接口NRMmzAllocCached
        "pupil_adjust_mask": false, // 是否调试OSD全屏Mask
        "pupil_adjust_component_pose": false, // 瞳距调节时是否更新外参
        "default_stereo_dp_src": false, // 是否默认双目dp_src
        "bypass_mode": false, // 是否bypass模式 (bypass 模式下dpisp的Downscaler不工作。GDC不对原图做任何变换，直通原图到display)
        "modify_dp_src": false, // 是否修改dp输入数据，用于调试
        "gen_depth_shifted_frame": false, // 是否生成depth shifted frame
        "async_shifted_frame": true, // 是否异步生成depth shifted frame
        "mmz_cached_for_shifted_frame": false, // 生成depth shifted frame时，是否使用cached mmz
        "register_signal_handler": false // 是否给task queue注册signal handler(开启后可使用kill -signal_num PID单独杀死一个线程)
    }
}